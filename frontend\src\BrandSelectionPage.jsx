import React from "react";
import { useNavigate } from "react-router-dom";

export default function BrandSelectionPage({ token }) {
  const navigate = useNavigate();

  const brands = ["DALSA", "FLIR", "OTHERS"];

  const handleBrandClick = (brand) => {
    // Store selected brand in sessionStorage for later use
    sessionStorage.setItem('selectedBrand', brand);

    if (brand === "DALSA") {
      navigate("/hierarchical-selection");
    } else if (brand === "FLIR") {
      // Navigate to hierarchical selection for FLIR as well
      navigate("/hierarchical-selection");
    } else if (brand === "OTHERS") {
      // Navigate to hierarchical selection for OTHERS as well
      navigate("/hierarchical-selection");
    }
  };

  return (
    <div style={{
      minHeight: "100vh",
      background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      padding: "2rem",
      fontFamily: "Arial, sans-serif"
    }}>
      <div style={{
        background: "rgba(255, 255, 255, 0.95)",
        borderRadius: "1.5rem",
        padding: "3rem",
        maxWidth: "700px",
        width: "100%",
        boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)",
        backdropFilter: "blur(10px)",
        textAlign: "center"
      }}>
        <h1 style={{
          color: "#1E3A8A",
          marginBottom: "1.5rem",
          fontSize: "2.5rem",
          fontWeight: "600"
        }}>
          Select Your Brand
        </h1>

        <p style={{
          fontSize: "1.2rem",
          color: "#4B5563",
          marginBottom: "2.5rem",
          lineHeight: "1.6"
        }}>
          Please select your camera brand to continue:
        </p>

        <div style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: "20px",
          alignItems: "center",
          maxWidth: "600px",
          margin: "0 auto"
        }}>
          {brands.map((brand) => (
            <button
              key={brand}
              onClick={() => handleBrandClick(brand)}
              style={{
                backgroundColor: "#3B82F6",
                color: "white",
                border: "none",
                padding: "16px 24px",
                fontSize: "1.1rem",
                borderRadius: "0.75rem",
                cursor: "pointer",
                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                transition: "all 0.2s ease-in-out",
                fontWeight: "600"
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = "#2563EB";
                e.target.style.transform = "translateY(-1px)";
                e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = "#3B82F6";
                e.target.style.transform = "translateY(0)";
                e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
              }}
            >
              {brand}
            </button>
          ))}
        </div>

        <div style={{ marginTop: "2.5rem" }}>
          <button
            onClick={() => navigate("/actions")}
            style={{
              padding: "12px 24px",
              backgroundColor: "#6B7280",
              color: "white",
              border: "none",
              borderRadius: "0.75rem",
              fontSize: "1rem",
              cursor: "pointer",
              transition: "all 0.2s ease-in-out",
              fontWeight: "600",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#4B5563";
              e.target.style.transform = "translateY(-1px)";
              e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#6B7280";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
            }}
          >
            ← Back to Actions
          </button>
        </div>
      </div>
    </div>
  );
}
