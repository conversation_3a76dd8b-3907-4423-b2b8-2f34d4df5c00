import weaviate
from weaviate.util import get_valid_uuid
from datetime import datetime

# ============ CONFIG ============
WEAVIATE_URL = "http://localhost:8080"  # Change if using cloud endpoint
DEST_CLASS = "SoftwareManualChunks"

client = weaviate.Client(WEAVIATE_URL)

# ============ SCHEMA DEFINITION ============
software_manual_schema = {
    "class": DEST_CLASS,
    "description": "Chunked sections from software manuals with metadata for retrieval",
    "vectorizer": "none",  # We'll handle embeddings separately
    "vectorIndexType": "hnsw",
    "vectorIndexConfig": {
        "distance": "cosine",
        "efConstruction": 128,
        "maxConnections": 64
    },
    "invertedIndexConfig": {
        "bm25": {"k1": 1.2, "b": 0.75},
        "stopwords": {"preset": "en"}
    },
    "properties": [
        # Core document metadata
        {"name": "source_file", "dataType": ["text"], "description": "Name of the source PDF/manual"},
        {"name": "file_hash", "dataType": ["text"], "description": "SHA256 hash of file for version control"},
        
        # Chunk information
        {"name": "chunk_number", "dataType": ["int"], "description": "Chunk index for ordered retrieval"},
        {"name": "page_number", "dataType": ["int"], "description": "Original page number in the PDF"},
        {"name": "section_title", "dataType": ["text"], "description": "Nearest heading or section title"},

        # Content
        {"name": "content", "dataType": ["text"], "description": "Text content from the manual"},

        # Software-specific metadata
        {"name": "software_version", "dataType": ["text"], "description": "Version of the software/manual"},
        {"name": "api_type", "dataType": ["text"], "description": "API type: C++, .NET, or C"},
        {"name": "module_name", "dataType": ["text"], "description": "Module or feature name"},
        
        # Time metadata
        {"name": "created_at", "dataType": ["date"], "description": "Timestamp of ingestion"}
    ],
    "moduleConfig": {}
}

# ============ CREATE CLASS ============
if not client.schema.contains(software_manual_schema):
    client.schema.create_class(software_manual_schema)
    print(f"✅ Created class: {DEST_CLASS}")
else:
    print(f"ℹ️ Class {DEST_CLASS} already exists")
